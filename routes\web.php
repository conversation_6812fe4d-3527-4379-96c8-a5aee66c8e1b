<?php


use App\Http\Controllers\BulletinController;
use App\Http\Controllers\ComplaintController;
use App\Http\Controllers\PlusCardCampaignController;
use App\Http\Controllers\PlusCardCampaignUsageController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SikayetController;
use App\Http\Controllers\ContractCodeController;
use App\Http\Controllers\ContractPeriodController;
use App\Http\Controllers\VehicleQueryController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PlusCardController;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\PromotionCodeController;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/login',[\App\Http\Controllers\UserController::class,'loginPage'])->name('loginPage');
Route::get('/landing',[\App\Http\Controllers\CustomerController::class,'landing'])->name('landing');
Route::get('/plus-card-agreement/{id}',[\App\Http\Controllers\PlusCardController::class,'plusCardAgreement'])->name('plusCardAgreement');
Route::get('/nearby-branches',[\App\Http\Controllers\BranchController::class,'nearbyBranches'])->name('nearbyBranches');
Route::post('/loginPost',[\App\Http\Controllers\UserController::class,'loginPost'])->name('loginPost');
Route::get('/loginPost', function () {
    return redirect()->route('loginPage')->with('error','Bir Hata Oluştu Tekrar Deneyiniz.');
});
Route::get('/sifremi-unuttum',[\App\Http\Controllers\HelperController::class,'forgotPassword'])->name('forgotPassword');
Route::get('/forgot-auth-code',[\App\Http\Controllers\HelperController::class,'forgotAuthCode'])->name('forgotAuthCode');
Route::post('/forgotPasswordPost',[\App\Http\Controllers\HelperController::class,'forgotPasswordPost'])->name('forgotPasswordPost');

Route::get('/listDriveFile/{id}',[\App\Http\Controllers\GoogleDriveController::class,'listFiles'])->name('listDriveFile');
Route::get('/google/callback',[\App\Http\Controllers\GoogleDriveController::class,'callback']);


Route::get('/startpay/{id}',[\App\Http\Controllers\PaymentController::class,'show'])->name('startpay');
Route::post('/paynet',[\App\Http\Controllers\PaymentController::class,'paynet'])->name('paynet');
Route::post('/paynetStatus',[\App\Http\Controllers\PaymentController::class,'status'])->name('status');
Route::post('/paynetCallback',[\App\Http\Controllers\PaymentController::class,'callBack'])->name('paynetCallback');


Route::get('/otosorgu/{uuid?}/{plaka?}/{type?}/{tc?}/{date?}/{degisenValue?}',[\App\Http\Controllers\OtosorguController::class,'hasar'])->name('otoSorguPlaka');
Route::get('/otosorgu_sasi/{uuid?}/{sasi?}/{type?}/{tc?}/{date?}/{degisenValue?}',[\App\Http\Controllers\OtosorguController::class,'sasi'])->name('otoSorguSasi');

Route::get('/arabasorgula/{uuid?}/{plaka?}/{type?}/{tc?}/{date?}/{degisenValue?}',[\App\Http\Controllers\ArabaSorgulaController::class,'hasar'])->name('araba-sorgula-plaka');
Route::get('/arabasorgula-sasi/{uuid?}/{sasi?}/{type?}/{tc?}/{date?}/{degisenValue?}',[\App\Http\Controllers\ArabaSorgulaController::class,'sasi'])->name('araba-sorgula-sasi');

Route::get("sikayetvar",[SikayetController::class,"index"]);

Route::post('/api/send_agreement_customer',[\App\Http\Controllers\HelperController::class,'sendAgreementCustomer'])->name('api.sendAgreementCustomer');
Route::post('/api/send_agreement_check',[\App\Http\Controllers\HelperController::class,'sendAgreementCheck'])->name('api.sendAgreementCheck');

Route::middleware('userAuth')->group(function (){
    Route::get('/', \App\Http\Controllers\DashboardController::class)->name('index');
    Route::post('/dashboard_report', [\App\Http\Controllers\DashboardController::class,'dashboard_report'])->name('dashboard_report');
    Route::get('/most-income-branches', [\App\Http\Controllers\DashboardController::class,'mostIncomeBranches'])->name('mostIncomeBranches');
    Route::get('/top-branch-cars', [DashboardController::class, 'topBranchCars'])->name('topBranchCars');

    Route::get('/expertise-details',[\App\Http\Controllers\ExpertiseController::class,'details'])->name('expertise_details');
    Route::middleware('throttle:5,1')->post('/updateExpertise',[\App\Http\Controllers\ExpertiseController::class,'updateExpertise'])->name('updateExpertise');
    Route::post('/updateExpertiseNote',[\App\Http\Controllers\ExpertiseController::class,'updateExpertiseNote'])->name('updateExpertiseNote');
    Route::post('/deleteExpertiseNote',[\App\Http\Controllers\ExpertiseController::class,'deleteExpertiseNote'])->name('deleteExpertiseNote');
    Route::post('/deleteExpertisePayment',[\App\Http\Controllers\ExpertiseController::class,'deleteExpertisePayment'])->name('deleteExpertisePayment');


    Route::get('/alici-sozlesme/{id}',[\App\Http\Controllers\ExpertiseController::class,'aliciSozlesme'])->name('aliciSozlesme');
    Route::get('/ekspertiz-raporu/{id}',[\App\Http\Controllers\ExpertiseController::class,'ekspertizRaporu'])->name('ekspertizRaporu');
    Route::get('/hasar-raporu/{id}',[\App\Http\Controllers\ExpertiseController::class,'hasarRaporu'])->name('hasarRaporu');
    Route::get('/taslak-arac-resmi/{id}',[\App\Http\Controllers\ExpertiseController::class,'taslakAracResmi'])->name('taslakAracResmi');
    Route::get('/expertise/taslak-arac-resmi/{uuid}', [\App\Http\Controllers\ExpertiseController::class, 'taslakAracResmi'])->name('expertise.taslakAracResmi'); 

    Route::get('/ekspertiz-ftp-goruntule/{uid}',[\App\Http\Controllers\ExpertiseController::class,'ekspertizFtpView'])->name('ekspertizFtpView');
    Route::get('/ekspertiz-islemleri',[\App\Http\Controllers\ExpertiseController::class,'ekspertizIslemleri'])->name('ekspertizIslemleri');
    Route::get('/kvkk-onay',[\App\Http\Controllers\HelperController::class,'kvkkOnay'])->name('kvkkOnay');
    Route::get('/menu',[\App\Http\Controllers\HelperController::class,'menu'])->name('menu');
    Route::get('/import-excel',[\App\Http\Controllers\HelperController::class,'importExcel'])->name('importExcel');
    Route::post('/import-excelPost',[\App\Http\Controllers\HelperController::class,'importExcelPost'])->name('importExcelPost');
    Route::post('/kvkk',[\App\Http\Controllers\HelperController::class,'kvkk'])->name('kvkk');
    Route::get('/report/{type}',[\App\Http\Controllers\HelperController::class,'report'])->name('reports');


    // Plus Card
    Route::post('/plus-cards-balance', [PlusCardController::class, 'plus_cards_balance'])->name('plus_cards_balance');
    Route::post('/plus-card/campaign/select', [PlusCardController::class, 'selectCampaign'])->name('pluscard.campaign.select');
    Route::get('/plus-card/duplicate-customers', [PlusCardController::class, 'plusCardDuplicateCustomers'])->name('plusCard.duplicateCustomers');
    Route::get('/plus-card/customers-with-multiple-cards', [PlusCardController::class, 'customersWithMultipleCards'])->name('plusCard.customersWithMultipleCards');
    Route::post('/plus-card/customer-note', [\App\Http\Controllers\PlusCardController::class, 'saveCustomerNote'])->name('plusCard.saveCustomerNote');
    Route::post('/plus-card/get-customer-note', [\App\Http\Controllers\PlusCardController::class, 'getCustomerNote'])->name('plusCard.getCustomerNote');

    // Complaints
    Route::get('/complaints/search', [ComplaintController::class, 'search'])->name('complaints.search');
    Route::post('/complaints/result-status/{complaint:talep_no}', [ComplaintController::class, 'updateResultStatus'])->name('complaints.resultStatus');

    Route::resources([
        'car-case-types' => \App\Http\Controllers\CarCaseTypeController::class,
        'car-fuels' => \App\Http\Controllers\CarFuelController::class,
        'car-gears' => \App\Http\Controllers\CarGearController::class,
        'car-groups' => \App\Http\Controllers\CarGroupController::class,
        'cars' => \App\Http\Controllers\CarController::class,
        'branches' => \App\Http\Controllers\BranchController::class,
        'branch-categories' => \App\Http\Controllers\BranchCategoryController::class,
        'branch-category-user-limits' => \App\Http\Controllers\BranchCategoryUserLimitController::class,
        'customers' => \App\Http\Controllers\CustomerController::class,
        'customer-groups' => \App\Http\Controllers\CustomerGroupController::class,
        'customer-types' => \App\Http\Controllers\CustomerTypeController::class,
        'campaigns' => \App\Http\Controllers\CampaignController::class,
        'stock-units' => \App\Http\Controllers\StockUnitController::class,
        'stock-groups' => \App\Http\Controllers\StockGroupController::class,
        'stock-types' => \App\Http\Controllers\StockTypeController::class,
        'stocks' => \App\Http\Controllers\StockController::class,
        'expertises' => \App\Http\Controllers\ExpertiseController::class,
        'users' => \App\Http\Controllers\UserController::class,
        'menus' => \App\Http\Controllers\MenuController::class,
        'user-role-groups' => \App\Http\Controllers\UserRoleGroupController::class,
        'notes' => \App\Http\Controllers\NoteController::class,
        'query' => \App\Http\Controllers\QueryController::class,
        'logs' => \App\Http\Controllers\LogController::class,
        'pool-questions' => \App\Http\Controllers\PoolQuestionController::class,
        'pool-choises' => \App\Http\Controllers\PoolChoiseController::class,
        'tickets' => \App\Http\Controllers\TicketController::class,
        'sliders' => \App\Http\Controllers\SliderController::class,
        'bookings' => \App\Http\Controllers\BookingController::class,
        'settings' => \App\Http\Controllers\SettingController::class,
        'zones' => \App\Http\Controllers\ZoneController::class,
        'plus-cards' => \App\Http\Controllers\PlusCardController::class,
        'complaints' => \App\Http\Controllers\ComplaintController::class,
        'invoice-definitions' => \App\Http\Controllers\InvoiceDefinitionController::class,
        'customer-feedbacks' => \App\Http\Controllers\CustomerFeedbackController::class,
        'plus-cards-definitions' => \App\Http\Controllers\PlusCardDefinitionsController::class,
        'contracts' => \App\Http\Controllers\ContractController::class,
        'plus-card-campaign-usages' => PlusCardCampaignUsageController::class,
        'plus-card-campaign' =>PlusCardCampaignController::class,
    ]);

    Route::post('/normAnalyze', [\App\Http\Controllers\BranchCategoryController::class, 'normAnalyze'])->name('normAnalyze');

    Route::post('/api/tcNoDogrula',[\App\Http\Controllers\HelperController::class,'tcNoDogrula'])->name('api.tcNoDogrula');
    Route::post('/api/personalTcControl',[\App\Http\Controllers\HelperController::class,'personalTcControl'])->name('api.personalTcControl');
    Route::post('/api/phoneControl',[\App\Http\Controllers\HelperController::class,'phoneControl'])->name('api.phoneControl');
    Route::post('/api/vergiNoKontrol',[\App\Http\Controllers\HelperController::class,'vergiNoKontrol'])->name('api.vergiNoKontrol');
    Route::post('/phoneControl',[\App\Http\Controllers\CustomerController::class,'phoneControl'])->name('phoneControl');
    Route::post('/api/getBranchesForPlusCard',[\App\Http\Controllers\BranchController::class,'getBranchesForPlusCard'])->name('api.getBranchesForPlusCard');
    Route::post('/api/getCustomersForPlusCard',[\App\Http\Controllers\CustomerController::class,'getCustomersForPlusCard'])->name('api.getCustomersForPlusCard');
    Route::post('/api/getCustomersForContract',[\App\Http\Controllers\CustomerController::class,'getCustomersForContract'])->name('api.getCustomersForContract');
    Route::post('/api/getCarBrands',[\App\Http\Controllers\CarGroupController::class,'getCarBrands'])->name('api.getCarBrands');
    Route::post('/api/getContractDetails',[\App\Http\Controllers\ContractController::class,'getContractDetails'])->name('api.getContractDetails');
    Route::post('/api/getCustomers',[\App\Http\Controllers\CustomerController::class,'getCustomers'])->name('api.getCustomers');
    Route::post('/api/getCustomerDetails',[\App\Http\Controllers\CustomerController::class,'getCustomerDetails'])->name('api.getCustomerDetails');
    Route::post('/api/getCars',[\App\Http\Controllers\CarController::class,'getCars'])->name('api.getCars');
    Route::post('/api/getCarDetails',[\App\Http\Controllers\CarController::class,'getCarDetails'])->name('api.getCarDetails');
    Route::post('/addCustomer',[\App\Http\Controllers\CustomerController::class,'addCustomer'])->name('api.addCustomer');
    Route::post('/addCar',[\App\Http\Controllers\CarController::class,'addCar'])->name('api.addCar');
    Route::post('/getCarExpertises',[\App\Http\Controllers\CarController::class,'getCarExpertises'])->name('api.getCarExpertises');
    Route::post('/getStocks',[\App\Http\Controllers\StockController::class,'getStocks'])->name('api.getStocks');
    Route::post('/autoExpertiseSave',[\App\Http\Controllers\ExpertiseController::class,'autoExpertiseSave'])->name('api.autoExpertiseSave');
    Route::post('/getNotes',[\App\Http\Controllers\NoteController::class,'getNotes'])->name('api.getNotes');
    Route::post('/getCarModels',[\App\Http\Controllers\CarGroupController::class,'getCarModels'])->name('api.getCarModels');
    Route::post('/getCarCaseType',[\App\Http\Controllers\CarCaseTypeController::class,'getCarCaseType'])->name('api.getCarCaseType');
    Route::post('/getStockTypeGroups',[\App\Http\Controllers\StockTypeController::class,'getStockTypeGroups'])->name('api.getStockTypeGroups');
    Route::post('/getTowns',[\App\Http\Controllers\HelperController::class,'getTowns'])->name('api.getTowns');
    Route::post('/deleteExpertise',[\App\Http\Controllers\ExpertiseController::class,'deleteExpertise'])->name('deleteExpertise');
    Route::post('/voiceRecord',[\App\Http\Controllers\HelperController::class,'voiceRecord'])->name('voiceRecord');
    Route::post('/getCustomersForAjax',[\App\Http\Controllers\CustomerController::class,'indexAjax'])->name('getCustomersForAjax');
    Route::post('/getPlusCardsForAjax',[\App\Http\Controllers\PlusCardController::class,'indexAjax'])->name('getPlusCardsForAjax');
    Route::post('/getPlusCardCampaignUsagesForAjax', [PlusCardCampaignUsageController::class, 'indexAjax'])->name('getPlusCardCampaignUsagesForAjax');
    Route::post('/getUsersForAjax', [\App\Http\Controllers\UserController::class, 'indexAjax'])->name('getUsersForAjax');
    Route::post('/getCustomersForSelect',[\App\Http\Controllers\CustomerController::class,'getCustomersForSelect'])->name('getCustomersForSelect');
    Route::post('/getCarsForAjax',[\App\Http\Controllers\CarController::class,'indexAjax'])->name('getCarsForAjax');
    Route::post('/getQueriesForAjax',[\App\Http\Controllers\QueryController::class,'indexAjax'])->name('getQueriesForAjax');
    Route::post('/saveFilters',[\App\Http\Controllers\HelperController::class,'saveFilters'])->name('saveFilters');
    Route::post('/deleteCache',[\App\Http\Controllers\HelperController::class,'deleteCache'])->name('deleteCache');
    Route::post('/getCustomerPlusCards',[\App\Http\Controllers\HelperController::class,'getCustomerPlusCards'])->name('api.getCustomerPlusCards');
    Route::post('/campaingPrices',[\App\Http\Controllers\HelperController::class,'campaingPrices'])->name('api.campaingPrices');
    Route::post('/expertiseReportDownload',[\App\Http\Controllers\ExpertiseController::class,'expertiseReportDownload'])->name('api.expertiseReportDownload');
    Route::post('/createOldExpertisDowland',[\App\Http\Controllers\HelperController::class,'createOldExpertisDowland'])->name('api.createOldExpertisDowland');
    Route::post('/checkOldExpertisDowland',[\App\Http\Controllers\HelperController::class,'checkOldExpertisDowland'])->name('api.checkOldExpertisDowland');
    Route::post('/PrintCikisTarihi',[\App\Http\Controllers\HelperController::class,'PrintCikisTarihi'])->name('api.PrintCikisTarihi');
    Route::post('/deleteStockCampaign',[\App\Http\Controllers\HelperController::class,'deleteStockCampaign'])->name('api.deleteStockCampaign');
    Route::post('/deletePlusCardBalance',[\App\Http\Controllers\HelperController::class,'deletePlusCardBalance'])->name('api.deletePlusCardBalance');
    Route::post('/deletePlusCard',[\App\Http\Controllers\HelperController::class,'deletePlusCard'])->name('api.deletePlusCard');
    Route::post('/api/plusCardBalance',[\App\Http\Controllers\HelperController::class,'plusCardBalance'])->name('api.plusCardBalance');
    Route::post('/plusCardsDefinitionsDelete',[\App\Http\Controllers\HelperController::class,'plusCardsDefinitionsDelete'])->name('api.plusCardsDefinitionsDelete');
    Route::post('/api/smsVerification',[\App\Http\Controllers\HelperController::class,'smsVerification'])->name('api.smsVerification');
    Route::post('/api/smsVerificationUse',[\App\Http\Controllers\HelperController::class,'smsVerificationUse'])->name('api.smsVerificationUse');
    Route::post('/api/expertiseFtpOk',[\App\Http\Controllers\HelperController::class,'expertiseFtpOk'])->name('api.expertiseFtpOk');
    Route::post('/api/user_information_update',[\App\Http\Controllers\HelperController::class,'userInformationUpdate'])->name('api.userInformationUpdate');
    Route::post('/api/send_agreement',[\App\Http\Controllers\HelperController::class,'sendAgreement'])->name('api.sendAgreement');
    Route::post('/api/send_agreement_check',[\App\Http\Controllers\HelperController::class,'sendAgreementCheck'])->name('api.sendAgreementCheck');
    Route::post('/getExpertisesForAjax',[\App\Http\Controllers\ExpertiseController::class,'indexAjax'])->name('getExpertisesForAjax');
    Route::post('/setExpertiseDownloaded',[\App\Http\Controllers\ExpertiseController::class,'setExpertiseDownloaded'])->name('api.setExpertiseDownloaded');
    Route::get('/excel_downloads',[\App\Http\Controllers\HelperController::class,'excelDownloads'])->name('excelDownloads');
    Route::post('/excel_download',[\App\Http\Controllers\HelperController::class,'excelDownload'])->name('excelDownload');
    Route::post('/setPlusCardSystemID',[\App\Http\Controllers\PlusCardController::class,'setPlusCardSystemID'])->name('setPlusCardSystemID');
    Route::post('/removePointOrCredits',[\App\Http\Controllers\PlusCardController::class,'removePointOrCredits'])->name('api.removePointOrCredits');
    Route::post('/checkDefinitionPrices',[\App\Http\Controllers\PlusCardController::class,'checkDefinitionPrices'])->name('api.checkDefinitionPrices');
    Route::post('/addBodyworkCoordinate',[\App\Http\Controllers\ExpertiseController::class,'addBodyworkCoordinate'])->name('api.addBodyworkCoordinate');
    Route::get('/plus-kart-raporu',[\App\Http\Controllers\PlusCardController::class,'plusCardReport'])->name('plusCardReport');
    Route::get('/plus-kart-puan',[\App\Http\Controllers\PlusCardController::class,'plusCardPoint'])->name('plusCardPoint');
    Route::post('/plus-kart-puan-update',[\App\Http\Controllers\PlusCardController::class,'plusCardPointUpdate'])->name('plusCardPointUpdate');
    Route::post('/plus-card-sms-verification',[\App\Http\Controllers\ExpertiseController::class,'plusCardSmsVerification'])->name('plusCardSmsVerification');
    // Contract
    Route::post('/check-contract',[\App\Http\Controllers\ContractController::class,'checkContract'])->name('checkContract');
    Route::post('/create-contract-code/{contract}',[\App\Http\Controllers\ContractController::class,'createContractCode'])->name('createContractCode');
    Route::post('/getContractPrices',[\App\Http\Controllers\ContractController::class,'getContractPrices'])->name('api.getContractPrices');
    Route::post('/endContractPeriod/',[\App\Http\Controllers\ContractController::class,'endContractPeriod'])->name('endContractPeriod');
    Route::post('/getContractCustomerDetails/',[\App\Http\Controllers\ContractController::class,'getContractCustomerDetails'])->name('getContractCustomerDetails');
    Route::post('/addContractPeriodLimit/',[\App\Http\Controllers\ContractController::class,'addContractPeriodLimit'])->name('addContractPeriodLimit');
    Route::get('/contract-details',[\App\Http\Controllers\ContractController::class,'contractDetails'])->name('contractDetails');
    Route::get('/contracts-periods',[\App\Http\Controllers\ContractController::class,'periods'])->name('contracts.periods');
    Route::get('/contract-codes', [ContractCodeController::class, 'indexAjax'])->name('contract.codes');
    Route::post('/contract-codes/{contractCode}', [ContractCodeController::class, 'destroy'])->name('contract.codes.destroy');
    Route::get('/contract-periods', [ContractPeriodController::class, 'indexAjax'])->name('contract.periods');
    // Vehicle Query
    Route::get('/vehicle-queries', [VehicleQueryController::class, 'index'])->name('vehicleQueries');
    Route::post('/vehicle-queries/ajax', [VehicleQueryController::class, 'indexAjax'])->name('vehicleQueries.ajax');
    Route::get('/vehicle-queries/create', [VehicleQueryController::class, 'create'])->name('vehicleQueries.create');
    Route::post('/vehicle-queries/search', [VehicleQueryController::class, 'search'])->name('vehicleQueries.search');
    //bulletin
    Route::get('bulletins',[BulletinController::class, 'index'])->name('bulletin.index');
    Route::get('bulletin/create',[BulletinController::class, 'create'])->name('bulletin.create');
    Route::post('bulletin/store',[BulletinController::class, 'store'])->name('bulletin.store');
    Route::post('bulletin/{bulletin}/mark-as-read', [BulletinController::class, 'markAsRead']);

    // Promotion Apply
    Route::post('promotion/apply', [PromotionCodeController::class, 'apply'])->name('promotion.apply');

    Route::post('/logout',[\App\Http\Controllers\UserController::class,'logout'])->name('logout');
});

Route::post('/checkListUpdate',[\App\Http\Controllers\HelperController::class,'checkListUpdate'])->name('checkListUpdate');

Route::prefix('musteri')->middleware('userAuth')->group(function (){
    Route::get('/plus-cart',[\App\Http\Controllers\Customer\HelperController::class,'plusKart'])->name('customer.plusCard');
    Route::get('/plus-cart-satin-al',[\App\Http\Controllers\Customer\HelperController::class,'plusCardBuy'])->name('customer.plusCardBuy');
    Route::post('/plus-cart-satin-al-post',[\App\Http\Controllers\Customer\HelperController::class,'plusCardBuyPost'])->name('customer.plusCardBuyPost');
    Route::get('/araclar',[\App\Http\Controllers\Customer\HelperController::class,'cars'])->name('customer.cars');
    Route::get('/ekspertiz-raporu/{id}',[\App\Http\Controllers\Customer\HelperController::class,'ekspertizRaporu'])->name('customer.ekspertizRaporu');
    Route::get('/arac-sorgu',[\App\Http\Controllers\Customer\HelperController::class,'query'])->name('customer.query');
    Route::get('/araclarim',[\App\Http\Controllers\Customer\HelperController::class,'my_query'])->name('customer.my_query');
    Route::get('/randevulariniz',[\App\Http\Controllers\Customer\HelperController::class,'booking'])->name('customer.booking');
    Route::get('/yeni-randevu',[\App\Http\Controllers\Customer\HelperController::class,'newBooking'])->name('customer.newBooking');
    Route::post('/randevu-iptal',[\App\Http\Controllers\Customer\HelperController::class,'deleteBooking'])->name('customer.deleteBooking');
    Route::get('/uzaktan-ekspertiz',[\App\Http\Controllers\Customer\HelperController::class,'remoteExpertise'])->name('customer.remoteExpertise');
    Route::get('/yeni-uzaktan-ekspertiz',[\App\Http\Controllers\Customer\HelperController::class,'newRemoteExpertise'])->name('customer.newRemoteExpertise');
    Route::get('/profilim',[\App\Http\Controllers\Customer\HelperController::class,'profile'])->name('customer.profile');
    Route::post('/updateProfile',[\App\Http\Controllers\Customer\HelperController::class,'updateProfile'])->name('customer.updateProfile');
    Route::resource('contracts',\App\Http\Controllers\Customer\ContractController::class)->names('customer.contracts');
    Route::get('/contract-status/{id}',[\App\Http\Controllers\Customer\ContractController::class,'contractStatus'])->name('customer.contractStatus');
    Route::post('/customerBuyExpertise',[\App\Http\Controllers\Customer\HelperController::class,'customerBuyExpertise'])->name('customer.customerBuyExpertise');
    Route::post('/deleteContractCode',[\App\Http\Controllers\Customer\HelperController::class,'deleteContractCode'])->name('customer.deleteContractCode');
    Route::post('/api/customerDeleteSmsConfirmation',[\App\Http\Controllers\Customer\HelperController::class,'customerDeleteSmsConfirmation'])->name('api.customerDeleteSmsConfirmation');
    Route::post('/customerBuyExpertisePaynetForm',[\App\Http\Controllers\Customer\HelperController::class,'customerBuyExpertisePaynetForm'])->name('customer.customerBuyExpertisePaynetForm');
    Route::post('/customerBuyPlusCardPaynetForm',[\App\Http\Controllers\Customer\HelperController::class,'customerBuyPlusCardPaynetForm'])->name('customer.customerBuyPlusCardPaynetForm');
});

Route::get('/getstoks',[\App\Http\Controllers\StockController::class,'getstoks']);

Route::middleware('userAuth')->prefix('excel')->group(function (){
    Route::get('/ekspertiz',[\App\Http\Controllers\ExpertiseController::class,'export']);
    Route::get('/subeler',[\App\Http\Controllers\BranchController::class,'export']);
    Route::get('/kampanyalar',[\App\Http\Controllers\CampaignController::class,'export']);
    Route::get('/kasa-turleri',[\App\Http\Controllers\CarCaseTypeController::class,'export']);
    Route::get('/yakit-turleri',[\App\Http\Controllers\CarFuelController::class,'export']);
    Route::get('/vites-turleri',[\App\Http\Controllers\CarGearController::class,'export']);
    Route::get('/arac-gruplari',[\App\Http\Controllers\CarGroupController::class,'export']);
    Route::get('/araclar',[\App\Http\Controllers\CarController::class,'export']);
    Route::get('/query',[\App\Http\Controllers\QueryController::class,'export']);
    Route::get('/cari-gruplari',[\App\Http\Controllers\CustomerGroupController::class,'export']);
    Route::get('/cariler',[\App\Http\Controllers\CustomerController::class,'export']);
    Route::get('/cari-turleri',[\App\Http\Controllers\CustomerTypeController::class,'export']);
    Route::get('/stok-gruplari',[\App\Http\Controllers\StockGroupController::class,'export']);
    Route::get('/stoklar',[\App\Http\Controllers\StockController::class,'export']);
    Route::get('/stok-turleri',[\App\Http\Controllers\StockTypeController::class,'export']);
    Route::get('/stok-birimleri',[\App\Http\Controllers\StockUnitController::class,'export']);
    Route::get('/kullanicilar',[\App\Http\Controllers\UserController::class,'export']);
    Route::get('/sikayetler',[\App\Http\Controllers\ComplaintController::class,'export']);
    Route::get('/plus_card',[\App\Http\Controllers\PlusCardController::class,'export']);
    Route::get('/plus-kart-raporu',[\App\Http\Controllers\PlusCardController::class,'reportExport']);
    Route::get('/plus_card_customer/{id}',[\App\Http\Controllers\PlusCardController::class,'plusCardCustomerExport'])->name('plusCardCustomerExport');
});

Route::prefix('online')->as('online.')->group(function (){
    Route::get('/',[\App\Http\Controllers\OnlineController::class,'index'])->name('index');
    Route::get('/login',[\App\Http\Controllers\OnlineController::class,'login'])->name('login');
    Route::post('/loginPost',[\App\Http\Controllers\OnlineController::class,'loginPost'])->name('loginPost');
    Route::get('/reports',[\App\Http\Controllers\OnlineController::class,'reports'])->name('reports')->middleware(['customerWebAuth']);
    Route::get('/plus-card/summary',[\App\Http\Controllers\OnlineController::class,'plusCardSummary'])->name('plusCardSummary')->middleware(['customerWebAuth']);
    Route::get('/plus-card/buy',[\App\Http\Controllers\OnlineController::class,'plusCardBuy'])->name('plusCardBuy')->middleware(['customerWebAuth']);
    Route::get('/booking/new',[\App\Http\Controllers\OnlineController::class,'bookingNew'])->name('bookingNew');
    Route::post('/booking/delete',[\App\Http\Controllers\OnlineController::class,'deleteBooking'])->name('deleteBooking');
    Route::get('/booking/index',[\App\Http\Controllers\OnlineController::class,'bookingIndex'])->name('bookingIndex')->middleware(['customerWebAuth']);
    Route::get('/remote/new',[\App\Http\Controllers\OnlineController::class,'remoteNew'])->name('remoteNew')->middleware(['customerWebAuth']);
    Route::get('/remote/index',[\App\Http\Controllers\OnlineController::class,'remoteIndex'])->name('remoteIndex')->middleware(['customerWebAuth']);
    Route::get('/profile',[\App\Http\Controllers\OnlineController::class,'profile'])->name('profile')->middleware(['customerWebAuth']);
    Route::get('/query',[\App\Http\Controllers\OnlineController::class,'query'])->name('query')->middleware(['customerWebAuth']);
    Route::post('/logout',[\App\Http\Controllers\OnlineController::class,'logout'])->name('logout')->middleware(['customerWebAuth']);
    Route::post('/booking/store',[\App\Http\Controllers\OnlineController::class,'bookingStore'])->name('bookingStore');
    Route::get('/is-emri',[\App\Http\Controllers\OnlineController::class,'isEmri'])->name('isEmri');
    Route::get('/gizlilik-politikasi',[\App\Http\Controllers\OnlineController::class,'gizlilikPolitikasi'])->name('gizlilikPolitikasi');
    Route::post('/remote/store',[\App\Http\Controllers\OnlineController::class,'remoteStore'])->name('remoteStore')->middleware(['customerWebAuth']);
    Route::get('/remote/show/{id}',[\App\Http\Controllers\OnlineController::class,'remoteShow'])->name('remoteShow')->middleware(['customerWebAuth']);
    Route::post('/profile/update',[\App\Http\Controllers\OnlineController::class,'profileUpdate'])->name('profileUpdate')->middleware(['customerWebAuth']);
    Route::post('/booking/hours',[\App\Http\Controllers\OnlineController::class,'branchBookingHours'])->name('branchBookingHours');
    Route::get('/contracts',[\App\Http\Controllers\OnlineController::class,'contracts'])->name('contracts');
    Route::get('/contracts/edit/{contract}',[\App\Http\Controllers\OnlineController::class,'contractsEdit'])->name('contracts.edit');
    Route::put('/contracts/update/{contract}',[\App\Http\Controllers\OnlineController::class,'contractsUpdate'])->name('contracts.update');
    Route::get('/contract-status/{id}/{status}',[\App\Http\Controllers\OnlineController::class,'contractStatus'])->name('contractStatus');
    Route::get('/expertises',[\App\Http\Controllers\OnlineController::class,'expertises'])->name('expertises');
    Route::get('/ekspertiz-raporu/{uuid}',[\App\Http\Controllers\OnlineController::class,'ekspertizRaporu'])->name('ekspertizRaporu');
    Route::post('/customerBuyPlusCardPaynetForm',[\App\Http\Controllers\OnlineController::class,'customerBuyPlusCardPaynetForm'])->name('customerBuyPlusCardPaynetForm');
    Route::post('/customerBuyExpertisePaynetForm',[\App\Http\Controllers\OnlineController::class,'customerBuyExpertisePaynetForm'])->name('customerBuyExpertisePaynetForm');
    Route::get('/contract-status/{id}',[\App\Http\Controllers\OnlineController::class,'contractStatus'])->name('contractStatus');
    Route::post('/create-contract-code/{contract}',[\App\Http\Controllers\OnlineController::class,'createContractCode'])->name('createContractCode');
    Route::post('/deleteContractCode',[\App\Http\Controllers\OnlineController::class,'deleteContractCode'])->name('deleteContractCode');
});

Route::get('/api/autoExpertiseFtpOk',[\App\Http\Controllers\ExpertiseController::class,'autoExpertiseFtpOk'])->name('api.autoExpertiseFtpOk');
Route::get('/api/deleteNotOpenedExpertises',[\App\Http\Controllers\ExpertiseController::class,'deleteNotOpenedExpertises'])->name('api.deleteNotOpenedExpertises');
Route::post('/api/getBranches',[\App\Http\Controllers\BranchController::class,'getBranches'])->name('api.getBranches');
Route::post('/callback',[\App\Http\Controllers\Customer\HelperController::class,'callback']);
Route::post('/saveToSession',[\App\Http\Controllers\HelperController::class,'saveToSession'])->name('saveToSession');
Route::get('/plusCardCreditPointValidDateUpdater',[\App\Http\Controllers\HelperController::class,'plusCardCreditPointValidDateUpdater']);
Route::post('/bayi-list-ajax', [\App\Http\Controllers\BranchController::class, 'indexAjax'])->name('bayiListAjax');
Route::post('/getPlusCardCampaignsForAjax', [PlusCardCampaignController::class, 'indexAjax'])->name('getPlusCardCampaignsForAjax');

Route::get('/plus-card-campaign/create', [PlusCardCampaignController::class, 'create'])->name('plus-card-campaign.create');
Route::get('/plus-card-campaign/{id}/edit', [PlusCardCampaignController::class, 'edit'])->name('plus-card-campaign.edit');
Route::match(['put', 'patch'], '/plus-card-campaign/{id}', [PlusCardCampaignController::class, 'update'])->name('plus-card-campaign.update');
Route::delete('/plus-card-campaign/{id}', [PlusCardCampaignController::class, 'destroy'])->name('plus-card-campaign.destroy');
Route::post('/plus-card/set-primary', [PlusCardController::class, 'setPrimary'])->name('plus-card.set-primary');

include 'extended_web.php';
