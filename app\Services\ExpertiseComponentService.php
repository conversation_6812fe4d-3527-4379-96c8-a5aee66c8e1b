<?php

namespace App\Services;

use App\Models\Expertise;
use App\Models\ExpertiseComponent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * Service ExpertiseComponentService
 *
 * @package Expertise
 */
class ExpertiseComponentService
{
    /**
     * Get expert controls
     *
     * @param Expertise $expertise
     * @return array
     */
    public function getExpertControls(Expertise $expertise): array
    {
        $controls = [];

        $savedControls = ExpertiseComponent::where('expertise_id', $expertise->id)->get()->keyBy('key');

        // Generate expert controls
        foreach (__('arrays.components') as $controlKey => $controlTitle) {
            $savedControl = $savedControls->get($controlKey);

            $controls[$controlKey] = [
                'status' => $savedControl?->status ?? 0,
                'answer' => $savedControl?->answer ?? '',
                'note' => $savedControl?->note ?? '',
                'title' => $controlTitle
            ];
        }

        return $controls;
    }

    /**
     * Save or update expertise component data.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Expertise $expertise
     * @return void
     */
    public function save(Request $request, Expertise $expertise): void
    {
        // Fetch existing components keyed by 'key'
        $existingComponents = ExpertiseComponent::where('expertise_id', $expertise->id)
            ->get()
            ->keyBy('key');

        $insertData = [];
        $updateData = [];
        $updateKeys = [];

        $excludedKeys = [
            '_token',
            'expertise_id',
            'close',
            'type',
            'date',
            'not',
            'komponent_kontrol_user',
            'submit',
            'measured_kw',
            'measured_hp',
            'calculated_kw',
            'calculated_hp',
            'calculated_rpm',
            'transfer_kw',
            'transfer_hp',
            'transfer_rpm',
        ];

        foreach ($request->all() as $key => $item) {
            if (in_array($key, $excludedKeys, true)) {
                continue;
            }

            /** @var ExpertiseComponent|null $component */
            $component = $existingComponents->get($key);

            if ($key === 'diferansiyel' || $key === 'tork_konventoru') {
                if (is_array($item) && array_key_exists(2, $item)) {
                    $answer = !empty($item[1]) ? $item[1] : '';
                    $note = $item[2];
                } else {
                    $answer = !empty($item[0]) ? $item[0] : '';
                    $note = $item[1] ?? '';
                }
            } else {
                $answer = is_array($item) && !empty($item[0]) ? $item[0] : '';
                $note = is_array($item) ? ($item[1] ?? '') : '';
            }

            $data = [
                'expertise_id' => $expertise->id,
                'key' => $key,
                'answer' => $answer,
                'note' => $note,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            if ($component) {
                // For update, remove 'created_at'
                unset($data['created_at']);
                $updateKeys[] = $component->id;
                $updateData[] = $data;
            } else {
                // For insert
                $insertData[] = $data;
            }
        }

        // Bulk insert
        if (!empty($insertData)) {
            ExpertiseComponent::insert($insertData);
        }

        // Bulk update
        if (!empty($updateData)) {
            $updateQuery = 'UPDATE expertise_components SET ';
            $updateColumns = ['answer', 'note', 'updated_at'];

            foreach ($updateColumns as $column) {
                $updateQuery .= "{$column} = CASE id ";
                foreach ($updateData as $index => $data) {
                    $safeValue = addslashes($data[$column]);
                    $updateQuery .= "WHEN {$updateKeys[$index]} THEN '{$safeValue}' ";
                }
                $updateQuery .= 'END, ';
            }

            // Remove trailing comma and space
            $updateQuery = rtrim($updateQuery, ', ') . ' WHERE id IN (' . implode(',', $updateKeys) . ')';

            DB::statement($updateQuery);
        }
    }
}
