<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class PromotionCode extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_id',
        'promo_code',
        'discount_type',
        'discount_value',
        'minimum_amount',
        'maximum_discount',
        'expires_at',
        'is_used',
        'used_at',
        'used_amount',
        'usage_details',
        'generated_source',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
        'is_used' => 'boolean',
        'usage_details' => 'array',
        'discount_value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'used_amount' => 'decimal:2',
    ];

    /**
     * Check if the promotion code is valid for use.
     */
    public function isValid(): bool
    {
        return !$this->is_used && Carbon::now()->lte($this->expires_at);
    }
}
