<?php

namespace App\Http\Controllers\Api\V1\Customer;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\DamageRequest;
use App\Http\Requests\Api\DetailRequest;
use App\Http\Requests\Api\ReplacementRequest;
use Illuminate\Http\JsonResponse;
use App\Models\Car;

/**
 * Class VehicleQueryController
 *
 * @package VehicleQuery
 */
class VehicleQueryController extends Controller
{
    /**
     * @param DamageRequest $request
     * @return JsonResponse
     */
    public function damage(DamageRequest $request): JsonResponse
    {
        // vin
        // plate
        $input = $request->validated();

        // borc
        $expertiseUuid = '9dc6f088-f8a8-496d-9c9c-986a573c8f81';

        // hasar
        $expertiseUuid = '9dc72e48-4bb9-473c-b6e3-0cd5893a5b9b';

//        Car::with();

        $response = app('App\Http\Controllers\ArabaSorgulaController')->hasar(
            $expertiseUuid,
            $input['value'],
            'hasar'
        );

        if (property_exists($response, 'original')) {
            return  response()->json(['success' => true, 'data' => $response->original]);
        }

        return  response()->json(['success' => false, 'data' => '']);
    }

    /**
     * @param DetailRequest $request
     * @return JsonResponse
     */
    public function detail(DetailRequest $request): JsonResponse
    {
        $input = $request->validated();

        $response = app('App\Http\Controllers\ArabaSorgulaController')->hasar(
            $expertiseUuid,
            $input['value'],
            'AracDetay'
        );

        if (property_exists($response, 'original')) {
            return  response()->json(['success' => true, 'data' => $response->original]);
        }

        return  response()->json(['success' => false, 'data' => '']);
    }

    /**
     * @param ReplacementRequest $request
     * @return JsonResponse
     */
    public function replacement(ReplacementRequest $request): JsonResponse
    {
        $input = $request->validated();

        $response = app('App\Http\Controllers\ArabaSorgulaController')->hasar(
            $expertiseUuid,
            $input['value'],
            'Degisen'
        );

        if (property_exists($response, 'original')) {
            return  response()->json(['success' => true, 'data' => $response->original]);
        }

        return  response()->json(['success' => false, 'data' => '']);
    }

    /**
     * @param ReplacementRequest $request
     * @return JsonResponse
     */
    public function kilometer(ReplacementRequest $request): JsonResponse
    {
        $input = $request->validated();

        $response = app('App\Http\Controllers\ArabaSorgulaController')->hasar(
            $expertiseUuid,
            $input['value'],
            'kilometre'
        );

        if (property_exists($response, 'original')) {
            return  response()->json(['success' => true, 'data' => $response->original]);
        }

        return  response()->json(['success' => false, 'data' => '']);
    }

    /**
     * @param ReplacementRequest $request
     * @return JsonResponse
     */
    public function debt(ReplacementRequest $request): JsonResponse
    {
        $input = $request->validated();

        $response = app('App\Http\Controllers\OtosorguController')->hasar(
            $expertiseUuid,
            $input['value'],
            'borc'
        );

        if (property_exists($response, 'original')) {
            return  response()->json(['success' => true, 'data' => $response->original]);
        }

        return  response()->json(['success' => false, 'data' => '']);
    }
}
