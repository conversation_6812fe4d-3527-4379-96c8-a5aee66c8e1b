<?php

namespace App\Services;

use App\Models\PromotionCode;

class PromotionCodeService
{
    public function validateCode(string $promoCode, ?int $customerId = null): PromotionCode|false
    {
        $query = PromotionCode::where('promo_code', $promoCode);
        if ($customerId) {
            $query->where('customer_id', $customerId);
        }
        $promotion = $query->first();

        if (!$promotion || !$promotion->isValid()) {
            return false;
        }

        return $promotion;
    }

    public function useCode(PromotionCode $promotion, ?float $usedAmount = null, $usageDetails = null): void
    {
        $promotion->is_used = true;
        $promotion->used_at = now();
        if ($usedAmount !== null) {
            $promotion->used_amount = $usedAmount;
        }
        if ($usageDetails !== null) {
            $promotion->usage_details = $usageDetails;
        }
        $promotion->save();
    }

    public function createForCustomer(array $data): PromotionCode
    {
        $promoCode = 'UMRAN-' . strtoupper(\Illuminate\Support\Str::random(6));
        return PromotionCode::create([
            'customer_id'      => $data['customer_id'],
            'promo_code'       => $promoCode,
            'discount_type'    => $data['discount_type'],
            'discount_value'   => $data['discount_value'],
            'minimum_amount'   => $data['minimum_amount'],
            'maximum_discount' => $data['maximum_discount'] ?? null,
            'expires_at'       => $data['expires_at'],
            'generated_source' => 'mobile_app',
        ]);
    }

    public function calculateDiscount(PromotionCode $promotion, float $amount): float
    {
        if ($promotion->discount_type === 'percentage') {
            $discount = $amount * ($promotion->discount_value / 100);
            if ($promotion->maximum_discount && $discount > $promotion->maximum_discount) {
                $discount = $promotion->maximum_discount;
            }
        } else {
            $discount = $promotion->discount_value;
        }
        return $discount;
    }
}
