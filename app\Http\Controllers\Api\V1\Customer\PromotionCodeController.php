<?php

namespace App\Http\Controllers\Api\V1\Customer;

use App\Http\Controllers\Controller;
use App\Services\PromotionCodeService;
use App\Models\PromotionCode;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PromotionCodeController extends Controller
{
    /**
     * Create a new promotion code for a customer.
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Services\PromotionCodeService $service
     * @return \Illuminate\Http\JsonResponse
     */
    public function createForCustomer(Request $request, PromotionCodeService $service): JsonResponse
    {
        $validated = $request->validate([
            'customer_id' => 'required|integer',
        ]);

        // Sabit veya varsayılan değerlerle doldurulacak alanlar
        $data = [
            'customer_id'      => $validated['customer_id'],
            'discount_type'    => 'fixed',
            'discount_value'   => 10,
            'minimum_amount'   => 500,
            'maximum_discount' => 200,
            'expires_at'       => now()->addYear(),
        ];

        $promotionCode = $service->createForCustomer($data);

        return response()->json([
            'success'        => true,
            'promotion_code' => $promotionCode,
        ]);
    }

    /**
     * List all promotion codes for a customer.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'customer_id' => 'required|integer',
        ]);

        $promotionCodes = PromotionCode::where('customer_id', $validated['customer_id'])
            ->orderByDesc('created_at')
            ->get();

        return response()->json([
            'promotion_codes' => $promotionCodes,
        ]);
    }
}
