<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('promotion_codes', function (Blueprint $table) {
            $table->id()->comment('Primary key');
            $table->unsignedBigInteger('customer_id')->comment('ID of the customer who owns the code');
            $table->string('promo_code')->unique()->comment('Unique promotion code');
            $table->enum('discount_type', ['percentage', 'fixed'])->default('percentage')->comment('Type of discount: percentage or fixed amount');
            $table->decimal('discount_value', 8, 2)->comment('Discount value (percentage or fixed amount)');
            $table->decimal('minimum_amount', 8, 2)->default(500)->comment('Minimum amount required to use the code');
            $table->decimal('maximum_discount', 8, 2)->nullable()->comment('Maximum discount that can be applied');
            $table->dateTime('expires_at')->comment('Expiration date and time of the code');
            $table->boolean('is_used')->default(false)->comment('Whether the code has been used');
            $table->dateTime('used_at')->nullable()->comment('Date and time when the code was used');
            $table->decimal('used_amount', 8, 2)->nullable()->comment('Amount for which the code was used');
            $table->text('usage_details')->nullable()->comment('Details about the usage of the code');
            $table->string('generated_source')->default('mobile_app')->comment('Source where the code was generated');
            $table->timestamps();

            $table->comment('Table for storing customer-specific promotion codes');
        });
    }

    public function down()
    {
        Schema::dropIfExists('promotion_codes');
    }
}; 