<?php

namespace App\Http\Controllers;

use App\Services\PromotionCodeService;
use Illuminate\Http\Request;

class PromotionCodeController extends Controller
{
    /**
     * Apply a promotion code for a customer (web).
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Services\PromotionCodeService $service
     * @return \Illuminate\Http\RedirectResponse
     */
    public function apply(Request $request, PromotionCodeService $service)
    {
        $promoCode = $request->input('promo_code');
        $customerId = $request->input('customer_id');
        $usedAmount = $request->input('discount_amount');
        $usageDetails = $request->input('usage_details');

        $promotion = $service->validateCode($promoCode, $customerId);

        if (!$promotion) {
            return response()->json([
                'success' => false,
                'message' => 'Geçersiz, süresi dolmuş veya zaten kullanılmış bir kod.',
            ], 400);
        }

        // if ($usedAmount < $promotion->minimum_amount) {
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Bu promosyon kodu için minimum tutarın altında bir tutar seçtiniz.',
        //     ], 400);
        // }

        // if ($usedAmount > $promotion->maximum_discount) {
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Bu promosyon kodu için maksimum indirim tutarını aştınız.',
        //     ], 400);
        // }

        // Calculate discount
        // $discount = $service->calculateDiscount($promotion, $usedAmount);
        $discount = $usedAmount;

        // Mark as used
        $service->useCode($promotion, $usedAmount, $usageDetails);

        // Promosyon kodu kullanıldıktan sonra, müşteri ID'sini session'da tutmak isteyebilirsiniz.
        // Bu, bir müşterinin aynı oturumda tekrar kod kullanmasını engellemek veya takip etmek için yararlı olabilir.
        // Aşağıda, customer_id'yi session'a ekleyen örnek bir kod bulunmaktadır:

        session()->put('promotion_code_used_customer_id', $customerId);

        return response()->json([
            'success' => true,
            'discount' => $discount,
            'campaignId' => 265,
        ]);
    }
}
